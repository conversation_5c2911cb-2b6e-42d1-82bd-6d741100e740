{"smtp_server": "smtp.gmail.com", "smtp_port": 587, "sender_email": "<EMAIL>", "sender_password": "demo_password_placeholder", "sender_name": "<PERSON>", "use_tls": true, "simulation_mode": true, "log_recipients": true, "training_disclaimer": true, "_comments": {"smtp_server": "SMTP server for sending emails (Gmail, Outlook, etc.)", "smtp_port": "SMTP port (587 for TLS, 465 for SSL, 25 for unencrypted)", "sender_email": "Email address to send from (must match SMTP credentials)", "sender_password": "App password or SMTP password (NOT your regular email password)", "sender_name": "Display name for the sender", "use_tls": "Whether to use TLS encryption (recommended: true)", "simulation_mode": "Safety feature - must be true to send emails", "log_recipients": "Whether to log sent emails for reporting", "training_disclaimer": "Whether to add training disclaimer to emails"}, "security_notes": {"app_passwords": "For Gmail, use App Passwords instead of your regular password", "two_factor": "Enable 2FA on your email account for security", "permissions": "Only use accounts authorized for training purposes", "monitoring": "Monitor email sending for abuse or unauthorized use"}, "setup_instructions": {"gmail": ["1. Enable 2-Factor Authentication on your Google account", "2. Go to Google Account settings > Security > App passwords", "3. Generate an app password for 'Mail'", "4. Use the generated 16-character password in this config"], "outlook": ["1. Enable 2-Factor Authentication on your Microsoft account", "2. Go to Security settings > App passwords", "3. Generate an app password for email", "4. Use smtp-mail.outlook.com as server with port 587"]}}