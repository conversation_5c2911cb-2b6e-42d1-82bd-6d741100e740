#!/usr/bin/env python3
"""
Demo Email Sender - Shows email content without actually sending
For demonstration and testing purposes only
"""

import json
import os
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

def load_email_template(template_type='html'):
    """Load email template from file."""
    template_file = f"../email/email_template.{template_type}"
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ Template file {template_file} not found.")
        return None

def load_metadata():
    """Load email metadata from JSON file."""
    try:
        with open('../email/email_metadata.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ Metadata file not found.")
        return {}

def create_demo_email(recipient_email, recipient_name="Valued Customer"):
    """Create the phishing simulation email for demonstration."""
    print("🔧 Creating phishing simulation email...")
    
    # Load templates and metadata
    html_template = load_email_template('html')
    text_template = load_email_template('txt')
    metadata = load_metadata()
    
    if not html_template or not text_template:
        print("❌ Could not load email templates.")
        return None
    
    # Customize templates with recipient information
    html_content = html_template.replace('[Your Name]', recipient_name)
    text_content = text_template.replace('[Recipient Email]', recipient_email)
    text_content = text_content.replace('[Current Date]', datetime.now().strftime('%B %d, %Y'))
    
    # Add training disclaimer
    disclaimer = "\n\n🎓 TRAINING SIMULATION - This is a security awareness exercise 🎓"
    html_content += f"<div style='background-color: #ffffcc; padding: 10px; margin: 20px 0; border: 2px solid #ffcc00;'><strong>{disclaimer}</strong></div>"
    text_content += disclaimer
    
    # Create email message
    msg = MIMEMultipart('alternative')
    msg['From'] = f"Sarah Mitchell <<EMAIL>>"
    msg['To'] = recipient_email
    msg['Subject'] = "Invoice #4271 - Urgent Payment Required"
    msg['Reply-To'] = "<EMAIL>"
    
    # Add custom headers for tracking
    msg['X-Phishing-Simulation'] = 'True'
    msg['X-Training-Campaign'] = f"Invoice-Phishing-{datetime.now().strftime('%Y%m%d')}"
    
    # Attach both HTML and text versions
    msg.attach(MIMEText(text_content, 'plain'))
    msg.attach(MIMEText(html_content, 'html'))
    
    # Add simulated attachment
    add_safe_attachment(msg)
    
    return msg, html_content, text_content

def add_safe_attachment(msg):
    """Add a safe simulation attachment."""
    attachment_path = "../attachment/Invoice4271_SIMULATION.txt"
    
    try:
        with open(attachment_path, 'rb') as attachment:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(attachment.read())
        
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            'attachment; filename= "Invoice4271.doc"'  # Appears as .doc but is safe .txt
        )
        msg.attach(part)
        print("✅ Safe simulation attachment added (appears as Invoice4271.doc)")
        
    except FileNotFoundError:
        print("⚠️  Simulation attachment file not found. Email will be sent without attachment.")

def save_email_preview(html_content, recipient_email):
    """Save HTML email preview to file."""
    preview_file = f"email_preview_{recipient_email.replace('@', '_at_').replace('.', '_')}.html"
    
    with open(preview_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"📧 Email preview saved to: {preview_file}")
    return preview_file

def display_email_info(msg, recipient_email, recipient_name):
    """Display comprehensive email information."""
    print("\n" + "="*60)
    print("📧 PHISHING SIMULATION EMAIL CREATED")
    print("="*60)
    
    print(f"\n📬 EMAIL DETAILS:")
    print(f"   To: {recipient_name} <{recipient_email}>")
    print(f"   From: {msg['From']}")
    print(f"   Subject: {msg['Subject']}")
    print(f"   Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📎 ATTACHMENTS:")
    attachments = []
    for part in msg.walk():
        if part.get_content_disposition() == 'attachment':
            filename = part.get_filename()
            if filename:
                attachments.append(filename)
    
    if attachments:
        for attachment in attachments:
            print(f"   📄 {attachment}")
    else:
        print("   (No attachments)")
    
    print(f"\n🎯 PHISHING INDICATORS:")
    print(f"   ⚠️  Urgency: 'Urgent Payment Required', '2 days overdue'")
    print(f"   ⚠️  Authority: Impersonates billing department")
    print(f"   ⚠️  Fear: Threatens service suspension")
    print(f"   ⚠️  Attachment: Requests opening document for 'details'")
    print(f"   ⚠️  Pressure: Demands immediate action")
    
    print(f"\n🛡️  SAFETY FEATURES:")
    print(f"   ✅ Training disclaimer included")
    print(f"   ✅ Safe attachment (no malware)")
    print(f"   ✅ Educational content only")
    print(f"   ✅ Simulation headers added")

def main():
    """Main demonstration function."""
    recipient_email = "<EMAIL>"
    recipient_name = "Duhok Nerwai"
    
    print("🎭 PHISHING SIMULATION EMAIL DEMONSTRATION")
    print("="*60)
    print("Creating a complete phishing simulation email for testing...")
    print("This will show you exactly what the recipient would receive.")
    print("="*60)
    
    # Create the email
    result = create_demo_email(recipient_email, recipient_name)
    if not result:
        print("❌ Failed to create email")
        return
    
    msg, html_content, text_content = result
    
    # Display email information
    display_email_info(msg, recipient_email, recipient_name)
    
    # Save HTML preview
    preview_file = save_email_preview(html_content, recipient_email)
    
    # Show text version preview
    print(f"\n📄 TEXT VERSION PREVIEW (First 500 characters):")
    print("-" * 50)
    print(text_content[:500] + "..." if len(text_content) > 500 else text_content)
    print("-" * 50)
    
    print(f"\n🌐 NEXT STEPS:")
    print(f"   1. Open {preview_file} in your browser to see the HTML email")
    print(f"   2. Review the attachment: ../attachment/Invoice4271_SIMULATION.txt")
    print(f"   3. Check the phishing indicators and training materials")
    print(f"   4. Use this for security awareness training")
    
    print(f"\n⚠️  REMEMBER:")
    print(f"   • This is for authorized training purposes only")
    print(f"   • Always provide immediate educational follow-up")
    print(f"   • Never use for malicious purposes")
    
    print(f"\n✅ DEMONSTRATION COMPLETE!")
    
    return preview_file

if __name__ == "__main__":
    main()
